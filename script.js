// ChatGPT Mobile Clone JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const messageInput = document.querySelector('.message-input');
    const sendBtn = document.querySelector('.send-btn');
    const voiceBtn = document.querySelector('.voice-btn');
    const addBtn = document.querySelector('.add-btn');
    const toolsBtn = document.querySelector('.tools-btn');
    const menuBtn = document.querySelector('.menu-btn');
    const shareBtn = document.querySelector('.share-btn');
    const modelSelector = document.querySelector('.model-selector');

    // Handle input focus and blur
    messageInput.addEventListener('focus', function() {
        this.parentElement.style.backgroundColor = '#5a5a5a';
    });

    messageInput.addEventListener('blur', function() {
        this.parentElement.style.backgroundColor = '#4a4a4a';
    });

    // Handle send button click
    sendBtn.addEventListener('click', function() {
        const message = messageInput.value.trim();
        if (message) {
            console.log('Sending message:', message);
            // Here you would typically send the message to a backend
            messageInput.value = '';
            messageInput.focus();
        }
    });

    // Handle Enter key press in input
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendBtn.click();
        }
    });

    // Handle voice button click
    voiceBtn.addEventListener('click', function() {
        console.log('Voice input clicked');
        // Here you would implement voice recording functionality
        this.style.color = '#ffffff';
        setTimeout(() => {
            this.style.color = '#a0a0a0';
        }, 200);
    });

    // Handle add button click
    addBtn.addEventListener('click', function() {
        console.log('Add button clicked');
        // Here you would implement file upload or other add functionality
    });

    // Handle tools button click
    toolsBtn.addEventListener('click', function() {
        console.log('Tools button clicked');
        // Here you would show tools menu
        this.style.backgroundColor = '#4a4a4a';
        setTimeout(() => {
            this.style.backgroundColor = 'transparent';
        }, 200);
    });

    // Handle menu button click
    menuBtn.addEventListener('click', function() {
        console.log('Menu button clicked');
        // Here you would show sidebar menu
    });

    // Handle share button click
    shareBtn.addEventListener('click', function() {
        console.log('Share button clicked');
        // Here you would implement share functionality
    });

    // Handle model selector click
    modelSelector.addEventListener('click', function() {
        console.log('Model selector clicked');
        // Here you would show model selection dropdown
        this.style.backgroundColor = '#4a4a4a';
        setTimeout(() => {
            this.style.backgroundColor = '#404040';
        }, 200);
    });

    // Auto-resize input based on content
    messageInput.addEventListener('input', function() {
        // Reset height to auto to get the correct scrollHeight
        this.style.height = 'auto';

        // Set the height to the scrollHeight, but limit it to a maximum
        const maxHeight = 120; // Maximum height in pixels
        const newHeight = Math.min(this.scrollHeight, maxHeight);
        this.style.height = newHeight + 'px';

        // Show/hide scrollbar if content exceeds max height
        if (this.scrollHeight > maxHeight) {
            this.style.overflowY = 'auto';
        } else {
            this.style.overflowY = 'hidden';
        }
    });

    // Handle window resize for responsive adjustments
    window.addEventListener('resize', function() {
        // Adjust layout if needed for different screen sizes
        const windowWidth = window.innerWidth;
        if (windowWidth < 480) {
            document.body.classList.add('mobile');
        } else {
            document.body.classList.remove('mobile');
        }
    });

    // Initial check for mobile
    if (window.innerWidth < 480) {
        document.body.classList.add('mobile');
    }

    // Prevent zoom on iOS when focusing input
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        messageInput.addEventListener('focus', function() {
            this.style.fontSize = '16px';
        });
    }

    // Add touch feedback for mobile devices
    function addTouchFeedback(element) {
        element.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });

        element.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        });

        element.addEventListener('touchcancel', function() {
            this.style.transform = 'scale(1)';
        });
    }

    // Apply touch feedback to buttons
    [sendBtn, voiceBtn, addBtn, toolsBtn, menuBtn, shareBtn, modelSelector].forEach(addTouchFeedback);

    // Simulate typing indicator (for demo purposes)
    function showTypingIndicator() {
        console.log('Showing typing indicator...');
        // Here you would show a typing indicator in the chat
    }

    // Focus input on page load
    messageInput.focus();
});

// Utility functions
function formatMessage(text) {
    // Basic text formatting for messages
    return text.replace(/\n/g, '<br>');
}

function getCurrentTimestamp() {
    return new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

// Export functions for potential use in other scripts
window.ChatGPTMobile = {
    formatMessage,
    getCurrentTimestamp
};