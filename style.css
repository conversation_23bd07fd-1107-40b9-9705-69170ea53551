/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #2f2f2f;
    color: #ffffff;
    height: 100vh;
    overflow: hidden;
}

.app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 100vw;
}

/* Header styles */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #2f2f2f;
    border-bottom: 1px solid #404040;
    height: 60px;
}

.menu-btn, .share-btn {
    background: none;
    border: none;
    color: #ffffff;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-btn:hover, .share-btn:hover {
    background-color: #404040;
}

.model-selector {
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: #404040;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.model-selector:hover {
    background-color: #4a4a4a;
}

.model-name {
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
}

.model-version {
    font-size: 14px;
    color: #a0a0a0;
    margin-left: 2px;
}

.dropdown-icon {
    color: #a0a0a0;
    margin-left: 4px;
}

/* Main content styles */
.main-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: #2f2f2f;
}

.welcome-section {
    text-align: center;
    max-width: 400px;
}

.welcome-title {
    font-size: 32px;
    font-weight: 400;
    line-height: 1.2;
    color: #ffffff;
    margin-bottom: 20px;
}

/* Input section styles */
.input-section {
    padding: 16px;
    background-color: #2f2f2f;
    border-top: 1px solid #404040;
}

.input-container {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #404040;
    border-radius: 24px;
    padding: 8px 12px;
    max-width: 100%;
}

.add-btn {
    background: none;
    border: none;
    color: #a0a0a0;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.add-btn:hover {
    background-color: #4a4a4a;
    color: #ffffff;
}

.tools-btn {
    background: none;
    border: none;
    color: #a0a0a0;
    padding: 8px 12px;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    flex-shrink: 0;
}

.tools-btn:hover {
    background-color: #4a4a4a;
    color: #ffffff;
}

.input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #4a4a4a;
    border-radius: 20px;
    padding: 0 16px;
    min-height: 44px;
}

.message-input {
    flex: 1;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 16px;
    outline: none;
    padding: 12px 0;
}

.message-input::placeholder {
    color: #a0a0a0;
}

.voice-btn {
    background: none;
    border: none;
    color: #a0a0a0;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.voice-btn:hover {
    background-color: #5a5a5a;
    color: #ffffff;
}

.send-btn {
    background-color: #5a5a5a;
    border: none;
    color: #ffffff;
    padding: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 44px;
    height: 44px;
}

.send-btn:hover {
    background-color: #6a6a6a;
}

.send-btn:active {
    transform: scale(0.95);
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .header {
        padding: 10px 12px;
    }

    .welcome-title {
        font-size: 28px;
    }

    .input-section {
        padding: 12px;
    }

    .input-container {
        padding: 6px 10px;
    }

    .message-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Focus states */
.message-input:focus {
    outline: none;
}

.input-wrapper:focus-within {
    background-color: #5a5a5a;
}

/* Animation for smooth interactions */
button {
    transition: all 0.2s ease;
}

button:active {
    transform: scale(0.95);
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #2f2f2f;
}

::-webkit-scrollbar-thumb {
    background: #5a5a5a;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6a6a6a;
}