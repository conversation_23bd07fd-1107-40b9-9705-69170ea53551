/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #2f2f2f;
    color: #ffffff;
    height: 100vh;
    overflow: hidden;
}

.app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 100vw;
}

/* Header styles */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #2f2f2f;
    border-bottom: 1px solid #404040;
    height: 60px;
}

.menu-btn, .share-btn {
    background: none;
    border: none;
    color: #ffffff;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-btn:hover, .share-btn:hover {
    background-color: #404040;
}

.model-selector {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.model-selector:hover {
    background-color: #404040;
    border-radius: 8px;
}

.model-name {
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
}

.model-version {
    font-size: 14px;
    color: #a0a0a0;
    margin-left: 2px;
}

.dropdown-icon {
    color: #a0a0a0;
    margin-left: 4px;
}

/* Main content styles */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #2f2f2f;
    overflow: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 768px;
    margin: 0 auto;
    width: 100%;
    overflow: hidden;
}

.welcome-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
}

.welcome-title {
    font-size: 32px;
    font-weight: 400;
    line-height: 1.2;
    color: #ffffff;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: none;
}

.message {
    margin-bottom: 24px;
    display: flex;
    gap: 12px;
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 15px;
    line-height: 1.4;
}

.message.user .message-content {
    background-color: #0084ff;
    color: #ffffff;
    border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
    background-color: #404040;
    color: #ffffff;
    border-bottom-left-radius: 4px;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
}

.typing-dots {
    display: flex;
    gap: 4px;
    padding: 12px 16px;
    background-color: #404040;
    border-radius: 18px;
    border-bottom-left-radius: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background-color: #a0a0a0;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        opacity: 0.3;
    }
    30% {
        opacity: 1;
    }
}

/* Input section styles */
.input-section {
    padding: 16px;
    background-color: #2f2f2f;
    border-top: 1px solid #404040;
}

.input-container {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #404040;
    border-radius: 24px;
    padding: 8px 12px;
    max-width: 100%;
}

.add-btn {
    background: none;
    border: none;
    color: #a0a0a0;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.add-btn:hover {
    background-color: #4a4a4a;
    color: #ffffff;
}

.tools-btn {
    background: none;
    border: none;
    color: #a0a0a0;
    padding: 8px 12px;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    flex-shrink: 0;
}

.tools-btn:hover {
    background-color: #4a4a4a;
    color: #ffffff;
}

.input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #4a4a4a;
    border-radius: 20px;
    padding: 0 16px;
    min-height: 44px;
}

.message-input {
    flex: 1;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 16px;
    outline: none;
    padding: 12px 0;
}

.message-input::placeholder {
    color: #a0a0a0;
}

.voice-btn {
    background: none;
    border: none;
    color: #a0a0a0;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.voice-btn:hover {
    background-color: #5a5a5a;
    color: #ffffff;
}

.send-btn {
    background-color: #5a5a5a;
    border: none;
    color: #ffffff;
    padding: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 44px;
    height: 44px;
}

.send-btn:hover {
    background-color: #6a6a6a;
}

.send-btn:active {
    transform: scale(0.95);
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .header {
        padding: 10px 12px;
    }

    .welcome-title {
        font-size: 28px;
    }

    .input-section {
        padding: 12px;
    }

    .input-container {
        padding: 6px 10px;
    }

    .message-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Focus states */
.message-input:focus {
    outline: none;
}

.input-wrapper:focus-within {
    background-color: #5a5a5a;
}

/* Animation for smooth interactions */
button {
    transition: all 0.2s ease;
}

button:active {
    transform: scale(0.95);
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #2f2f2f;
}

::-webkit-scrollbar-thumb {
    background: #5a5a5a;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6a6a6a;
}