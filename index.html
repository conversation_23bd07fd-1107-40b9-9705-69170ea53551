<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <button class="menu-btn">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>

            <div class="model-selector">
                <span class="model-name">ChatGPT</span>
                <span class="model-version">4o</span>
                <svg class="dropdown-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 6L8 10L12 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>

            <button class="share-btn">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.684 13.342C8.886 12.938 9 12.482 9 12C9 11.518 8.886 11.062 8.684 10.658M8.684 13.342L15.316 17.658M8.684 13.342C8.174 14.300 7.135 15 6 15C4.343 15 3 13.657 3 12C3 10.343 4.343 9 6 9C7.135 9 8.174 9.7 8.684 10.658M15.316 17.658C15.674 17.438 16 17.048 16 16.5C16 15.672 15.328 15 14.5 15C13.672 15 13 15.672 13 16.5C13 17.328 13.672 18 14.5 18C15.048 18 15.438 17.674 15.658 17.316M15.316 17.658L8.684 10.658" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="welcome-section">
                <h1 class="welcome-title">What's on your<br>mind today?</h1>
            </div>
        </main>

        <!-- Input Section -->
        <div class="input-section">
            <div class="input-container">
                <button class="add-btn">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 4V16M4 10H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>

                <button class="tools-btn">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2.5 7.5L10 2.5L17.5 7.5L10 12.5L2.5 7.5Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M2.5 12.5L10 17.5L17.5 12.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span>Tools</span>
                </button>

                <div class="input-wrapper">
                    <input type="text" class="message-input" placeholder="Ask anything">
                    <button class="voice-btn">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10 1.25C8.27411 1.25 6.875 2.64911 6.875 4.375V10C6.875 11.7259 8.27411 13.125 10 13.125C11.7259 13.125 13.125 11.7259 13.125 10V4.375C13.125 2.64911 11.7259 1.25 10 1.25Z" stroke="currentColor" stroke-width="1.5"/>
                            <path d="M4.375 8.75V10C4.375 13.1066 6.89339 15.625 10 15.625C13.1066 15.625 15.625 13.1066 15.625 10V8.75" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                            <path d="M10 15.625V18.75" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                            <path d="M7.5 18.75H12.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>

                <button class="send-btn">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 3.33334L16.6667 10L10 16.6667" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M3.33334 10H16.6667" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>